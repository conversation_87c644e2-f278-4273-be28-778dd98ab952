import { APIClient } from '../api/client'
import type { APIResponse, TaskStatus, Message } from '../../types/api'

export interface MessageRequest {
  content: string
  context_html?: string
}

export interface TaskStatusData {
  id: string
  status: TaskStatus
  progress: number
  error_message?: string
  last_activity: string
  is_processing: boolean
  message_count: number
}

export interface MessageData {
  id: string
  role: string
  content: string
  timestamp: string
  has_code_snippet: boolean
}

export type ConnectionStatus = 'idle' | 'connecting' | 'connected' | 'processing' | 'error'

export class HTTPChatService extends APIClient {
  private pollingInterval: number | null = null
  private currentTaskId: string | null = null
  private listeners: {
    statusChange: ((status: TaskStatusData) => void)[]
    messageUpdate: ((messages: MessageData[]) => void)[]
    connectionChange: ((status: ConnectionStatus) => void)[]
    error: ((error: string) => void)[]
  } = {
    statusChange: [],
    messageUpdate: [],
    connectionChange: [],
    error: []
  }

  /**
   * 连接到任务（开始轮询）
   */
  async connect(taskId: string): Promise<void> {
    try {
      this.currentTaskId = taskId
      this.notifyConnectionChange('connecting')
      
      // 立即获取一次状态
      await this.pollTaskStatus()
      
      // 开始轮询
      this.startPolling()
      
      this.notifyConnectionChange('connected')
    } catch (error) {
      this.notifyConnectionChange('error')
      this.notifyError(error instanceof Error ? error.message : 'Connection failed')
      throw error
    }
  }

  /**
   * 断开连接（停止轮询）
   */
  disconnect(): void {
    this.stopPolling()
    this.currentTaskId = null
    this.notifyConnectionChange('idle')
  }

  /**
   * 发送消息
   */
  async sendMessage(content: string, contextHtml?: string): Promise<void> {
    if (!this.currentTaskId) {
      throw new Error('Not connected to any task')
    }

    try {
      this.notifyConnectionChange('processing')
      
      const encodedTaskId = encodeURIComponent(this.currentTaskId)
      const response = await this.post<APIResponse<any>>(
        `/api/tasks/${encodedTaskId}/messages`,
        {
          content,
          context_html: contextHtml || ''
        }
      )

      if (!response.success) {
        throw new Error('Failed to send message')
      }

      // 立即轮询状态更新
      await this.pollTaskStatus()
      
    } catch (error) {
      this.notifyConnectionChange('error')
      this.notifyError(error instanceof Error ? error.message : 'Failed to send message')
      throw error
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatusData> {
    const encodedTaskId = encodeURIComponent(taskId)
    const response = await this.get<APIResponse<TaskStatusData>>(`/api/tasks/${encodedTaskId}/status`)

    if (!response.success || !response.data) {
      throw new Error('Failed to get task status')
    }

    return response.data
  }

  /**
   * 获取任务消息
   */
  async getTaskMessages(taskId: string, limit: number = 50): Promise<MessageData[]> {
    const encodedTaskId = encodeURIComponent(taskId)
    const response = await this.get<APIResponse<MessageData[]>>(
      `/api/tasks/${encodedTaskId}/messages?limit=${limit}`
    )

    if (!response.success || !response.data) {
      throw new Error('Failed to get task messages')
    }

    return response.data
  }

  /**
   * 取消任务处理
   */
  async cancelTask(taskId: string): Promise<boolean> {
    const response = await this.post<APIResponse<{ cancelled: boolean }>>(
      `/api/tasks/${taskId}/cancel`,
      {}
    )
    
    return response.success && response.data?.cancelled === true
  }

  /**
   * 开始轮询
   */
  private startPolling(): void {
    this.stopPolling() // 确保没有重复的轮询
    
    const poll = async () => {
      if (!this.currentTaskId) return
      
      try {
        await this.pollTaskStatus()
        await this.pollMessages()
      } catch (error) {
        console.error('Polling error:', error)
        this.notifyError(error instanceof Error ? error.message : 'Polling failed')
      }
    }

    // 立即执行一次
    poll()
    
    // 设置定时轮询
    this.pollingInterval = window.setInterval(poll, this.getPollingInterval())
  }

  /**
   * 停止轮询
   */
  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
  }

  /**
   * 轮询任务状态
   */
  private async pollTaskStatus(): Promise<void> {
    if (!this.currentTaskId) return

    try {
      const status = await this.getTaskStatus(this.currentTaskId)
      this.notifyStatusChange(status)
      
      // 根据状态更新连接状态
      if (status.is_processing) {
        this.notifyConnectionChange('processing')
      } else {
        this.notifyConnectionChange('connected')
      }
      
    } catch (error) {
      console.error('Failed to poll task status:', error)
    }
  }

  /**
   * 轮询消息更新
   */
  private async pollMessages(): Promise<void> {
    if (!this.currentTaskId) return

    try {
      const messages = await this.getTaskMessages(this.currentTaskId)
      this.notifyMessageUpdate(messages)
    } catch (error) {
      console.error('Failed to poll messages:', error)
    }
  }

  /**
   * 获取轮询间隔（根据状态动态调整）
   */
  private getPollingInterval(): number {
    // 处理中时更频繁轮询，空闲时较少轮询
    return 2000 // 2秒间隔
  }

  // 事件监听器管理
  onStatusChange(callback: (status: TaskStatusData) => void): () => void {
    this.listeners.statusChange.push(callback)
    return () => {
      const index = this.listeners.statusChange.indexOf(callback)
      if (index > -1) this.listeners.statusChange.splice(index, 1)
    }
  }

  onMessageUpdate(callback: (messages: MessageData[]) => void): () => void {
    this.listeners.messageUpdate.push(callback)
    return () => {
      const index = this.listeners.messageUpdate.indexOf(callback)
      if (index > -1) this.listeners.messageUpdate.splice(index, 1)
    }
  }

  onConnectionChange(callback: (status: ConnectionStatus) => void): () => void {
    this.listeners.connectionChange.push(callback)
    return () => {
      const index = this.listeners.connectionChange.indexOf(callback)
      if (index > -1) this.listeners.connectionChange.splice(index, 1)
    }
  }

  onError(callback: (error: string) => void): () => void {
    this.listeners.error.push(callback)
    return () => {
      const index = this.listeners.error.indexOf(callback)
      if (index > -1) this.listeners.error.splice(index, 1)
    }
  }

  // 通知方法
  private notifyStatusChange(status: TaskStatusData): void {
    this.listeners.statusChange.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('Error in status change callback:', error)
      }
    })
  }

  private notifyMessageUpdate(messages: MessageData[]): void {
    this.listeners.messageUpdate.forEach(callback => {
      try {
        callback(messages)
      } catch (error) {
        console.error('Error in message update callback:', error)
      }
    })
  }

  private notifyConnectionChange(status: ConnectionStatus): void {
    this.listeners.connectionChange.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('Error in connection change callback:', error)
      }
    })
  }

  private notifyError(error: string): void {
    this.listeners.error.forEach(callback => {
      try {
        callback(error)
      } catch (error) {
        console.error('Error in error callback:', error)
      }
    })
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.disconnect()
    this.listeners.statusChange = []
    this.listeners.messageUpdate = []
    this.listeners.connectionChange = []
    this.listeners.error = []
  }
}

// 创建HTTP聊天服务实例
export const httpChatService = new HTTPChatService()
