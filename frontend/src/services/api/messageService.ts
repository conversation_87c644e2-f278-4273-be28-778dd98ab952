import { apiClient } from './client'
import type { Message } from '../../types/api'

export const messageService = {
  // 获取任务的消息历史
  getTaskMessages: async (taskId: string): Promise<Message[]> => {
    try {
      console.log(`Fetching messages for task: ${taskId}`)
      const response: any = await apiClient.get(`/api/tasks/${taskId}`)
      console.log('Raw API response:', response)

      // 直接访问response.data.messages
      if (response?.data?.messages && Array.isArray(response.data.messages)) {
        const messages = response.data.messages as Message[]
        console.log(`Successfully loaded ${messages.length} messages:`, messages)
        return messages
      }

      console.log('No messages found in response.data.messages, returning empty array')
      return []
    } catch (error) {
      console.error('Failed to fetch task messages:', error)
      // 返回空数组作为fallback
      return []
    }
  },

  // 发送消息
  sendMessage: async (taskId: string, content: string, contextHtml?: string): Promise<Message> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const response = await apiClient.post(`/api/tasks/${encodedTaskId}/messages`, {
      content,
      context_html: contextHtml
    }) as { data: Message }
    return response.data
  },

  // 删除消息
  deleteMessage: async (taskId: string, messageId: string): Promise<void> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const encodedMessageId = encodeURIComponent(messageId)
    await apiClient.delete(`/api/tasks/${encodedTaskId}/messages/${encodedMessageId}`)
  },

  // 重新生成消息
  regenerateMessage: async (taskId: string, messageId: string): Promise<Message> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const encodedMessageId = encodeURIComponent(messageId)
    const response = await apiClient.post(`/api/tasks/${encodedTaskId}/messages/${encodedMessageId}/regenerate`) as { data: Message }
    return response.data
  }
}
